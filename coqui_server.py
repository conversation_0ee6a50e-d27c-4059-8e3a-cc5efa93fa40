#!/usr/bin/env python3
"""
Simple Coqui TTS Server for Voice Cloning
This server provides voice cloning capabilities using Coqui TTS
"""

import os
import io
import json
import tempfile
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import torch
from TTS.api import TTS
import numpy as np
import soundfile as sf
import logging

# Fix PyTorch weights loading issue
import torch.serialization
from TTS.tts.configs.xtts_config import XttsConfig
from TTS.tts.models.xtts import XttsAudioConfig
torch.serialization.add_safe_globals([XttsConfig, XttsAudioConfig])

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize TTS model
tts = None

def initialize_tts():
    """Initialize the TTS model"""
    global tts
    try:
        # Use XTTS v2 for voice cloning
        model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
        logger.info(f"Loading TTS model: {model_name}")

        # Set weights_only=False to bypass PyTorch security restrictions
        import torch
        original_load = torch.load
        torch.load = lambda *args, **kwargs: original_load(*args, **kwargs, weights_only=False)

        tts = TTS(model_name)

        # Restore original torch.load
        torch.load = original_load

        logger.info("TTS model loaded successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to load TTS model: {e}")
        return False

@app.route('/api/tts', methods=['GET', 'POST'])
def text_to_speech():
    """Generate speech from text"""
    try:
        if request.method == 'GET':
            # Health check
            return jsonify({
                "status": "ready",
                "model": "xtts_v2",
                "message": "Coqui TTS server is running"
            })
        
        # Handle POST request for TTS generation
        if request.content_type == 'application/json':
            data = request.get_json()
            text = data.get('text', '')
            speaker_wav = data.get('speaker_wav')
            language = data.get('language', 'en')
        else:
            # Handle form data
            text = request.form.get('text', '')
            language = request.form.get('language', 'en')
            speaker_wav = None
            
            # Check if speaker_wav file is uploaded
            if 'speaker_wav' in request.files:
                speaker_file = request.files['speaker_wav']
                if speaker_file.filename:
                    # Save uploaded file temporarily
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                        speaker_file.save(tmp_file.name)
                        speaker_wav = tmp_file.name

        if not text:
            return jsonify({"error": "Text is required"}), 400

        logger.info(f"Generating speech for text: {text[:50]}...")
        
        # Generate speech
        if speaker_wav:
            # Voice cloning with speaker reference
            logger.info(f"Using speaker reference: {speaker_wav}")
            wav = tts.tts(text=text, speaker_wav=speaker_wav, language=language)
        else:
            # Default voice
            wav = tts.tts(text=text, language=language)
        
        # Convert to audio file
        audio_buffer = io.BytesIO()
        sf.write(audio_buffer, wav, 22050, format='WAV')
        audio_buffer.seek(0)
        
        # Clean up temporary file if created
        if speaker_wav and os.path.exists(speaker_wav):
            os.unlink(speaker_wav)
        
        return send_file(
            audio_buffer,
            mimetype='audio/wav',
            as_attachment=False,
            download_name='speech.wav'
        )
        
    except Exception as e:
        logger.error(f"TTS generation error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/tts/speaker-similarity', methods=['POST'])
def speaker_similarity_tts():
    """Generate speech with speaker similarity (voice cloning)"""
    try:
        text = request.form.get('text', '')
        language = request.form.get('language', 'en')
        
        if not text:
            return jsonify({"error": "Text is required"}), 400
            
        if 'speaker_wav' not in request.files:
            return jsonify({"error": "Speaker audio file is required"}), 400
            
        speaker_file = request.files['speaker_wav']
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            speaker_file.save(tmp_file.name)
            speaker_wav_path = tmp_file.name
        
        logger.info(f"Voice cloning with speaker file: {speaker_wav_path}")
        
        # Generate speech with voice cloning
        wav = tts.tts(text=text, speaker_wav=speaker_wav_path, language=language)
        
        # Convert to audio file
        audio_buffer = io.BytesIO()
        sf.write(audio_buffer, wav, 22050, format='WAV')
        audio_buffer.seek(0)
        
        # Clean up temporary file
        os.unlink(speaker_wav_path)
        
        return send_file(
            audio_buffer,
            mimetype='audio/wav',
            as_attachment=False,
            download_name='cloned_speech.wav'
        )
        
    except Exception as e:
        logger.error(f"Speaker similarity TTS error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/tts/info', methods=['GET'])
def get_server_info():
    """Get server information and available models"""
    try:
        return jsonify({
            "models": ["tts_models/multilingual/multi-dataset/xtts_v2"],
            "languages": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh-cn", "ja", "hu", "ko"],
            "speakers": [],
            "status": "ready"
        })
    except Exception as e:
        logger.error(f"Server info error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "model_loaded": tts is not None
    })

if __name__ == '__main__':
    logger.info("Starting Coqui TTS Server...")
    
    # Initialize TTS model
    if not initialize_tts():
        logger.error("Failed to initialize TTS model. Exiting.")
        exit(1)
    
    logger.info("Starting Flask server on port 5002...")
    app.run(host='0.0.0.0', port=5002, debug=False)
