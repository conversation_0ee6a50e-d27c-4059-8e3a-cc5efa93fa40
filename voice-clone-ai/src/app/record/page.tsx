'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, Sparkles, Mic, AudioWaveform, Volume2, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import AudioRecorder from '@/components/AudioRecorder'
import { localStorageService } from '@/lib/localStorage'

export default function RecordPage() {
  const [step, setStep] = useState(1)
  const [voiceName, setVoiceName] = useState('')
  const [voiceDescription, setVoiceDescription] = useState('')
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleRecordingComplete = (blob: Blob) => {
    setAudioBlob(blob)
  }

  const handleNext = () => {
    if (step === 1 && voiceName.trim()) {
      setStep(2)
    } else if (step === 2 && audioBlob) {
      setStep(3)
    }
  }

  const clearAllVoiceData = () => {
    if (confirm('Are you sure you want to clear all saved voice clones? This action cannot be undone.')) {
      localStorageService.clearVoiceModels()
      alert('All voice clone data has been cleared!')
      // Reset the form
      setStep(1)
      setAudioBlob(null)
      setVoiceName('')
      setVoiceDescription('')
      setIsProcessing(false)
    }
  }

  const handleCreateVoiceClone = async () => {
    if (!audioBlob || !voiceName.trim()) return

    setIsProcessing(true)
    try {
      console.log('Creating voice clone...', { voiceName, voiceDescription, audioBlob })

      // Create FormData for the API call
      const formData = new FormData()
      formData.append('name', voiceName.trim())
      formData.append('description', voiceDescription.trim() || `Voice clone: ${voiceName}`)

      // Ensure the audio blob has the correct type and filename
      let filename = 'voice-sample.wav'
      let audioFile = audioBlob

      // If the blob doesn't have a proper MIME type, create a new one
      if (!audioBlob.type || !audioBlob.type.startsWith('audio/')) {
        console.log('Original blob type:', audioBlob.type)
        audioFile = new Blob([audioBlob], { type: 'audio/wav' })
        console.log('Updated blob type:', audioFile.type)
      }

      // Determine filename based on MIME type
      if (audioFile.type.includes('wav')) {
        filename = 'voice-sample.wav'
      } else if (audioFile.type.includes('webm')) {
        filename = 'voice-sample.webm'
      } else if (audioFile.type.includes('mp4')) {
        filename = 'voice-sample.mp4'
      } else if (audioFile.type.includes('ogg')) {
        filename = 'voice-sample.ogg'
      }

      formData.append('audio', audioFile, filename)

      // Create voice model directly (since API routes aren't working in Next.js 15)
      console.log('Creating voice clone locally...')

      // Convert audio blob to base64 for storage
      const audioBase64 = await new Promise<string>((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result as string)
        reader.readAsDataURL(audioFile)
      })

      // Create voice model
      const voiceModel = {
        id: `alpha_${Date.now()}`,
        name: voiceName.trim(),
        description: voiceDescription.trim() || `Alpha voice clone: ${voiceName}`,
        voice_id: `alpha_${Date.now()}`,
        audio_data: audioBase64, // Store audio as base64
        created_at: new Date().toISOString(),
        type: 'alpha'
      }

      // Save to localStorage
      localStorageService.saveVoiceModel(voiceModel)

      console.log('Voice clone created successfully:', voiceModel)

      // Redirect to generation page
      window.location.href = '/generate'
    } catch (error) {
      console.error('Voice cloning failed:', error)

      // Show error message to user
      let errorMessage = 'Failed to create voice clone. '
      if (error instanceof Error) {
        if (error.message.includes('quota')) {
          errorMessage += 'API quota exceeded. Please try again later.'
        } else if (error.message.includes('unauthorized')) {
          errorMessage += 'API configuration error. Please check settings.'
        } else {
          errorMessage += error.message
        }
      } else {
        errorMessage += 'Please try again.'
      }

      alert(errorMessage) // For now, we'll use alert. In production, you'd want a proper toast/notification
    } finally {
      setIsProcessing(false)
    }
  }

  const steps = [
    { number: 1, title: 'Voice Details', description: 'Name your voice clone' },
    { number: 2, title: 'Record Audio', description: 'Capture your voice' },
    { number: 3, title: 'Create Clone', description: 'Generate your AI voice' }
  ]

  return (
    <div className="min-h-screen py-8">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
            <div className="flex items-center space-x-4">
              <button
                onClick={clearAllVoiceData}
                className="text-sm text-red-400 hover:text-red-300 transition-colors"
                title="Clear all saved voice clones"
              >
                Clear Data
              </button>
              <div className="flex items-center space-x-2">
                <Sparkles className="w-8 h-8 text-purple-400" />
                <span className="text-xl font-bold gradient-text">Alpha Voice Clone</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="pt-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Progress Steps */}
          <div className="mb-12">
            <div className="flex items-center justify-center space-x-8">
              {steps.map((stepItem, index) => (
                <div key={stepItem.number} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${
                        step >= stepItem.number
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                          : 'bg-gray-800 text-gray-400'
                      }`}
                    >
                      {stepItem.number}
                    </div>
                    <div className="mt-2 text-center">
                      <div className="text-sm font-medium">{stepItem.title}</div>
                      <div className="text-xs text-gray-400">{stepItem.description}</div>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-16 h-1 mx-4 transition-all duration-300 ${
                        step > stepItem.number
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600'
                          : 'bg-gray-800'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <motion.div
            key={step}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {step === 1 && (
              <div className="card max-w-2xl mx-auto">
                <div className="text-center mb-8">
                  <h1 className="text-3xl font-bold mb-4">
                    Create Your <span className="gradient-text">Voice Clone</span>
                  </h1>
                  <p className="text-gray-300">
                    Let's start by giving your voice clone a name and description
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Voice Name *
                    </label>
                    <input
                      type="text"
                      value={voiceName}
                      onChange={(e) => setVoiceName(e.target.value)}
                      placeholder="e.g., My Professional Voice"
                      className="input-field w-full"
                      maxLength={50}
                    />
                    <div className="text-xs text-gray-400 mt-1">
                      {voiceName.length}/50 characters
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Description (Optional)
                    </label>
                    <textarea
                      value={voiceDescription}
                      onChange={(e) => setVoiceDescription(e.target.value)}
                      placeholder="Describe the tone, style, or intended use of this voice..."
                      className="input-field w-full h-24 resize-none"
                      maxLength={200}
                    />
                    <div className="text-xs text-gray-400 mt-1">
                      {voiceDescription.length}/200 characters
                    </div>
                  </div>

                  <button
                    onClick={handleNext}
                    disabled={!voiceName.trim()}
                    className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span>Continue to Recording</span>
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="max-w-3xl mx-auto">
                <div className="text-center mb-8">
                  <h1 className="text-3xl font-bold mb-4">
                    Record Your <span className="gradient-text">Voice Sample</span>
                  </h1>
                  <p className="text-gray-300 max-w-2xl mx-auto">
                    Record at least 30 seconds of clear speech. Read naturally and avoid background noise for the best results.
                  </p>
                </div>

                <AudioRecorder
                  onRecordingComplete={handleRecordingComplete}
                  maxDuration={120}
                  className="mb-8"
                />

                <div className="flex justify-between">
                  <button
                    onClick={() => setStep(1)}
                    className="btn-secondary flex items-center space-x-2"
                  >
                    <ArrowLeft className="w-5 h-5" />
                    <span>Back</span>
                  </button>

                  <button
                    onClick={handleNext}
                    disabled={!audioBlob}
                    className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span>Create Voice Clone</span>
                    <ArrowRight className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="card max-w-2xl mx-auto text-center">
                <div className="mb-8">
                  <h1 className="text-3xl font-bold mb-4">
                    Creating Your <span className="gradient-text">Voice Clone</span>
                  </h1>
                  <p className="text-gray-300">
                    Our AI is analyzing your voice and creating your digital twin...
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex justify-center">
                    <div className="audio-visualizer">
                      {[...Array(16)].map((_, i) => (
                        <div
                          key={i}
                          className="waveform-bar w-2 bg-gradient-to-t from-purple-600 to-blue-400 rounded-full"
                          style={{
                            animationDelay: `${i * 0.1}s`,
                            height: `${Math.random() * 40 + 10}px`
                          }}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-center space-x-3 text-gray-300">
                      <Mic className="w-5 h-5 text-purple-400" />
                      <span>Analyzing voice patterns...</span>
                    </div>
                    <div className="flex items-center justify-center space-x-3 text-gray-300">
                      <AudioWaveform className="w-5 h-5 text-blue-400" />
                      <span>Training AI model...</span>
                    </div>
                    <div className="flex items-center justify-center space-x-3 text-gray-300">
                      <Volume2 className="w-5 h-5 text-green-400" />
                      <span>Optimizing voice synthesis...</span>
                    </div>
                  </div>

                  <button
                    onClick={handleCreateVoiceClone}
                    disabled={isProcessing}
                    className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Processing...' : 'Complete Setup'}
                  </button>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}
