import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Alpha Voice Clone - Clone Your Voice in Seconds",
  description: "Create realistic voice clones using advanced AI technology. Generate speech in your own voice from any text.",
  keywords: "voice cloning, AI voice, text to speech, voice synthesis, artificial intelligence",
  authors: [{ name: "Alpha Voice Clone" }],
  openGraph: {
    title: "Alpha Voice Clone - Clone Your Voice in Seconds",
    description: "Create realistic voice clones using advanced AI technology",
    type: "website",
    url: "https://alpha-voice-clone.vercel.app",
  },
  twitter: {
    card: "summary_large_image",
    title: "Alpha Voice Clone - Clone Your Voice in Seconds",
    description: "Create realistic voice clones using advanced AI technology",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} antialiased bg-black text-white min-h-screen`}>
        <div className="relative min-h-screen">
          {/* Background gradient */}
          <div className="fixed inset-0 bg-gradient-to-br from-purple-900/20 via-black to-blue-900/20 pointer-events-none" />

          {/* Content */}
          <div className="relative z-10">
            {children}
          </div>
        </div>
      </body>
    </html>
  );
}
